require('dotenv').config();

console.log('=== Parameter Store Test ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('AWS_REGION:', process.env.AWS_REGION);
console.log(
  'AWS_ACCESS_KEY_ID:',
  process.env.AWS_ACCESS_KEY_ID ? '[SET]' : '[NOT SET]',
);
console.log(
  'AWS_SECRET_ACCESS_KEY:',
  process.env.AWS_SECRET_ACCESS_KEY ? '[SET]' : '[NOT SET]',
);
console.log('PREFIX:', process.env.AWS_PARAMETER_STORE_PREFIX);
console.log('ENABLED:', process.env.AWS_PARAMETER_STORE_ENABLED);

async function testParameterStore() {
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
    console.error('❌ AWS credentials are not set');
    return;
  }

  const {
    SSMClient,
    GetParametersByPathCommand,
  } = require('@aws-sdk/client-ssm');

  const ssmClient = new SSMClient({
    region: process.env.AWS_REGION || 'eu-west-1',
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });

  try {
    console.log('\n🔍 Testing Parameter Store connection...');

    const command = new GetParametersByPathCommand({
      Path: process.env.AWS_PARAMETER_STORE_PREFIX || '/reach/prod/api',
      Recursive: true,
      WithDecryption: true,
      MaxResults: 5,
    });

    console.log('📡 Sending request to AWS...');
    const startTime = Date.now();

    const response = await ssmClient.send(command);
    const endTime = Date.now();

    console.log(`✅ Request completed in ${endTime - startTime}ms`);
    console.log('Parameters found:', response.Parameters?.length || 0);

    if (response.Parameters && response.Parameters.length > 0) {
      console.log('\n📝 Sample parameters (names only):');
      response.Parameters.slice(0, 5).forEach((param, index) => {
        console.log(`  ${index + 1}. ${param.Name}`);
      });
    } else {
      console.log(
        '⚠️  No parameters found at path:',
        process.env.AWS_PARAMETER_STORE_PREFIX,
      );
    }
  } catch (error) {
    console.error('❌ Error accessing Parameter Store:');
    console.error('  Name:', error.name);
    console.error('  Message:', error.message);
    console.error('  Code:', error.Code || error.code);
    if (error.$metadata) {
      console.error('  HTTP Status:', error.$metadata.httpStatusCode);
      console.error('  Request ID:', error.$metadata.requestId);
    }
  }
}

testParameterStore().catch(console.error);
