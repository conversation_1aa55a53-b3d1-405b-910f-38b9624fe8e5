import { bootstrapConfig } from './aws/bootstrap-config';
import { Logger } from '@nestjs/common';

async function initializeApplication() {
  const logger = new Logger('ApplicationInitialization');
  logger.log('🚀 Application Initialization: Loading configuration...');

  try {
    // STEP 1: Load configuration FIRST (Parameter Store in production, .env in development)
    await bootstrapConfig();
    logger.log('✅ Configuration loaded successfully');

    // STEP 2: Verify critical environment variables are set
    const port = process.env.PORT;
    const nodeEnv = process.env.NODE_ENV;

    if (!port) {
      throw new Error(
        'PORT environment variable is not set after configuration loading',
      );
    }

    logger.log(`📋 Configuration Summary:`);
    logger.log(`   Environment: ${nodeEnv}`);
    logger.log(`   Port: ${port}`);
    logger.log(
      `   Database: ${process.env.DATABASE_URL ? '[SET]' : '[NOT SET]'}`,
    );

    // STEP 3: Now start the NestJS application with loaded configuration
    logger.log('🚀 Starting NestJS application...');
    const { bootstrap } = await import('./main.js');
    await bootstrap();
  } catch (error: any) {
    console.error('💥 FATAL ERROR: Application initialization failed');
    console.error('');
    console.error('📋 Error Summary:');
    console.error(`   Message: ${error?.message || 'Unknown error'}`);
    console.error(`   Type: ${error?.name || 'Unknown'}`);
    console.error(`   Environment: ${process.env.NODE_ENV || 'undefined'}`);
    console.error('');

    if (error?.stack) {
      console.error('📚 Stack Trace:');
      console.error(error.stack);
      console.error('');
    }

    console.error('🚨 Application startup terminated');
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  initializeApplication();
}
