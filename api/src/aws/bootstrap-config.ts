/* eslint-disable no-console */
import { ParameterStoreConfigProvider } from './parameter-store-config.provider';
import * as dotenv from 'dotenv';

export async function bootstrapConfig(): Promise<void> {
  console.log('🔧 Bootstrap: Initializing configuration loading...');

  // First, load .env file to get NODE_ENV if not already set
  if (!process.env.NODE_ENV) {
    console.log(
      '🔧 NODE_ENV not set, loading .env file to determine environment...',
    );
    dotenv.config();
  }

  const nodeEnv = process.env.NODE_ENV;
  console.log(`📋 Environment: ${nodeEnv || 'undefined'}`);

  if (nodeEnv === 'production') {
    console.log('🏭 Production environment detected');
    console.log('📡 Parameter Store loading is MANDATORY for production');
    console.log(
      '🚨 Application will NOT start if Parameter Store fails to load',
    );
    console.log('📡 Loading configuration from AWS Parameter Store...');

    try {
      // MANDATORY Parameter Store loading - NO fallback in production
      const parameterStoreConfig =
        await ParameterStoreConfigProvider.loadParametersAndMergeWithEnv();

      // Override process.env with Parameter Store values
      Object.assign(process.env, parameterStoreConfig);

      // Verify critical values were loaded from Parameter Store
      const port = process.env.PORT;
      const dbUrl = process.env.DATABASE_URL;
      const awsRegion = process.env.AWS_REGION;

      console.log('✅ Parameter Store configuration loaded successfully');
      console.log(`   PORT from Parameter Store: ${port}`);
      console.log(`   DATABASE_URL: ${dbUrl ? '[SET]' : '[NOT SET]'}`);
      console.log(`   AWS_REGION: ${awsRegion}`);
      console.log(
        `   Total parameters loaded: ${Object.keys(parameterStoreConfig).length}`,
      );
    } catch (error: any) {
      // CRITICAL FAILURE - Parameter Store is mandatory in production
      console.error(
        '💥 CRITICAL ERROR: Parameter Store loading failed in production environment',
      );
      console.error(
        '🚨 Application startup ABORTED - Parameter Store is mandatory for production',
      );
      console.error('');
      console.error('📋 Error Details:');
      console.error(`   Error Type: ${error?.name || 'Unknown'}`);
      console.error(
        `   Error Message: ${error?.message || 'No message available'}`,
      );
      console.error(
        `   Error Code: ${error?.code || error?.Code || 'No code available'}`,
      );
      console.error('');
      console.error('🔍 Parameter Store Configuration:');
      console.error(`   Path: /reach/prod/api/`);
      console.error(`   Region: ${process.env.AWS_REGION || 'eu-west-1'}`);
      console.error(`   Environment: ${nodeEnv}`);
      console.error('');
      console.error('🛠️  Troubleshooting Steps:');
      console.error('   1. Verify AWS credentials and IAM permissions');
      console.error('   2. Check Parameter Store path: /reach/prod/api/');
      console.error('   3. Ensure parameters exist in eu-west-1 region');
      console.error('   4. Verify network connectivity to AWS Parameter Store');
      console.error('   5. Check CloudWatch logs for detailed AWS API errors');
      console.error('');
      console.error('❌ Application startup failed - exiting with code 1');

      // Exit immediately - NO fallback in production
      process.exit(1);
    }
  } else {
    console.log('🔧 Development environment detected');
    console.log('📄 Loading configuration from .env file only...');

    // Load .env file for development
    dotenv.config();

    console.log('✅ .env configuration loaded successfully');
    console.log(`   PORT from .env: ${process.env.PORT}`);
  }

  // Final validation
  if (!process.env.PORT) {
    const errorMsg = `PORT environment variable is required but not set (Environment: ${nodeEnv})`;
    console.error(`❌ Configuration validation failed: ${errorMsg}`);
    throw new Error(errorMsg);
  }

  if (!process.env.NODE_ENV) {
    const errorMsg = 'NODE_ENV environment variable is required but not set';
    console.error(`❌ Configuration validation failed: ${errorMsg}`);
    throw new Error(errorMsg);
  }

  console.log('🎯 Configuration loading completed successfully');
  console.log(`   Final PORT value: ${process.env.PORT}`);
  console.log(`   Final NODE_ENV value: ${process.env.NODE_ENV}`);
}
