import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  UseGuards,
  Query,
} from '@nestjs/common';
import { SkillsService } from './skills.service';
import { RoleGuard } from '@/guards/role.guard';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  SkillDto,
  SkillParam,
  SkillCategoryDto,
  SkillCategoryParam,
  AddStudentSkillDto,
  SkillQueryParamsDto,
} from './skills.dto';
import { UseRoles } from 'nest-access-control';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { SkillRoutes } from '@app/shared/constants/skills.constants';
import { User } from '@/guards/user.decorator';
import { type User as UserDecoratorType } from '@/db/schema';

@Controller({ version: '1', path: 'skills' })
@ApiTags('Skills')
export class SkillsController {
  private readonly logger = new Logger(SkillsController.name);

  constructor(private readonly skillsService: SkillsService) {}
  @Post(SkillRoutes.ADD_SKILL)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill', action: 'create', possession: 'any' })
  @ApiResponse({
    status: 201,
    description: 'The skill has been successfully created.',
  })
  @CLIENT_TYPE(AppClients.WEB)
  @ApiBearerAuth()
  async addSkill(@Body() skillDto: SkillDto): Promise<SkillParam> {
    try {
      return await this.skillsService.addSkill(skillDto);
    } catch (error: any) {
      this.logger.error('Error adding skill', error.stack);
      throw error;
    }
  }

  @Post(SkillRoutes.ADD_STUDENT_SKILL)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'student_skill', action: 'create', possession: 'own' })
  @CLIENT_TYPE(AppClients.MOBILE)
  async addStudentSkill(
    @Body() studentSkillDto: AddStudentSkillDto,
    @User() user: UserDecoratorType,
  ) {
    try {
      if (!user.student_profile) {
        this.logger.error('User does not have a student profile');
        throw new BadRequestException('User does not have a student profile');
      }
      this.logger.log(
        `Adding skill for student with id ${user.student_profile.id}`,
      );

      return await this.skillsService.addStudentSkill({
        studentId: user.student_profile.id,
        ...studentSkillDto,
      });
    } catch (error: any) {
      this.logger.error(
        `Error adding skill for student with id user id ${user.id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(SkillRoutes.DELETE_STUDENT_SKILL)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'student_skill', action: 'delete', possession: 'own' })
  @CLIENT_TYPE(AppClients.MOBILE)
  async deleteStudentSkill(
    @Param('skillId', new CustomParseUUIDPipe()) skillId: string,
    @User() user: UserDecoratorType,
  ) {
    if (!user.student_profile) {
      this.logger.error('User does not have a student profile');
      throw new BadRequestException('User does not have a student profile');
    }
    this.logger.log(
      `Deleting skill with id ${skillId} for student with id ${user.student_profile.id}`,
    );
    try {
      if (
        user.student_profile &&
        user.student_profile.id !== user.student_profile.id
      ) {
        this.logger.log(
          `User is requesting to delete skill for student with id ${user.student_profile.id}`,
        );
        throw new BadRequestException(
          'Students cannot delete skills for other students',
        );
      }
      return await this.skillsService.removeStudentSkill(
        user.student_profile.id,
        skillId,
      );
    } catch (error: any) {
      this.logger.error(
        `Error deleting skill with id ${skillId} for student with id ${user.student_profile.id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(SkillRoutes.GET_ALL_SKILLS)
  @ApiResponse({
    status: 200,
    description: 'The skills have been successfully fetched.',
  })
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill', action: 'read', possession: 'any' })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getAllSkills(
    @Query() query: SkillQueryParamsDto,
  ): Promise<SkillParam[]> {
    this.logger.log('Fetching all skills');
    try {
      return await this.skillsService.getAllSkills(
        query as SkillQueryParamsDto & {
          sort: keyof SkillParam;
        },
      );
    } catch (error: any) {
      this.logger.error('Error fetching all skills', error.stack);
      throw error;
    }
  }

  @Get(SkillRoutes.GET_SKILLS_BY_CATEGORY)
  @ApiResponse({
    status: 200,
    description: 'The skills by category have been successfully fetched.',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getSkillsByCategory(
    @Param('categoryId', new CustomParseUUIDPipe()) categoryId: string,
  ): Promise<SkillParam[]> {
    this.logger.log(`Fetching skills for category ${categoryId}`);
    try {
      return await this.skillsService.getSkillsByCategory(categoryId);
    } catch (error: any) {
      this.logger.error(
        `Error fetching skills for category ${categoryId}`,
        error.stack,
      );
      throw error;
    }
  }

  @Put(SkillRoutes.UPDATE_SKILL)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill', action: 'update', possession: 'any' })
  @CLIENT_TYPE(AppClients.WEB)
  async updateSkill(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() skillDto: SkillDto,
  ): Promise<SkillParam> {
    this.logger.log(`Updating skill with id ${id}`);

    try {
      return await this.skillsService.updateSkill(id, skillDto);
    } catch (error: any) {
      this.logger.error(`Error updating skill with id ${id}`, error.stack);
      throw error;
    }
  }

  @Delete(SkillRoutes.DELETE_SKILL)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill', action: 'delete', possession: 'own' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async deleteSkill(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User() user: UserDecoratorType,
  ) {
    this.logger.log(`Deleting skill with id ${id}`);

    try {
      return await this.skillsService.deleteSkill(id, user);
    } catch (error: any) {
      this.logger.error(`Error deleting skill with id ${id}`, error.stack);
      throw error;
    }
  }

  // Skill Categories endpoints
  @Post(SkillRoutes.ADD_SKILL_CATEGORY)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill_category', action: 'create', possession: 'any' })
  @ApiResponse({
    status: 201,
    description: 'The skill category has been successfully created.',
  })
  @CLIENT_TYPE(AppClients.WEB)
  @ApiBearerAuth()
  async addSkillCategory(
    @Body() skillCategoryDto: SkillCategoryDto,
  ): Promise<SkillCategoryParam> {
    try {
      return await this.skillsService.addSkillCategory(skillCategoryDto);
    } catch (error: any) {
      this.logger.error('Error adding skill category', error.stack);
      throw error;
    }
  }

  @Get(SkillRoutes.GET_SKILL_CATEGORY_BY_ID)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getSkillCategory(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<SkillCategoryParam> {
    this.logger.log(`Fetching skill category with id ${id}`);

    try {
      return await this.skillsService.getSkillCategoryById(id);
    } catch (error: any) {
      this.logger.error(
        `Error fetching skill category with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(SkillRoutes.GET_ALL_SKILL_CATEGORIES)
  @ApiResponse({
    status: 200,
    description: 'The skill categories have been successfully fetched.',
  })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getAllSkillCategories(): Promise<SkillCategoryParam[]> {
    this.logger.log('Fetching all skill categories');
    try {
      return await this.skillsService.getAllSkillCategories();
    } catch (error: any) {
      this.logger.error('Error fetching all skill categories', error.stack);
      throw error;
    }
  }
  @Put(SkillRoutes.UPDATE_SKILL_CATEGORY)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill_category', action: 'update', possession: 'any' })
  @CLIENT_TYPE(AppClients.WEB)
  async updateSkillCategory(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() skillCategoryDto: SkillCategoryDto,
  ): Promise<SkillCategoryParam> {
    this.logger.log(`Updating skill category with id ${id}`);

    try {
      return await this.skillsService.updateSkillCategory(id, skillCategoryDto);
    } catch (error: any) {
      this.logger.error(
        `Error updating skill category with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(SkillRoutes.DELETE_SKILL_CATEGORY)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill_category', action: 'delete', possession: 'any' })
  @CLIENT_TYPE(AppClients.WEB)
  async deleteSkillCategory(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ) {
    this.logger.log(`Deleting skill category with id ${id}`);

    try {
      return await this.skillsService.deleteSkillCategory(id);
    } catch (error: any) {
      this.logger.error(
        `Error deleting skill category with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(SkillRoutes.GET_SKILL_BY_ID)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getSkill(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<SkillParam> {
    this.logger.log(`Fetching skill with id ${id}`);

    try {
      return await this.skillsService.getSkillById(id);
    } catch (error: any) {
      this.logger.error(`Error fetching skill with id ${id}`, error.stack);
      throw error;
    }
  }
}
