# AWS Parameter Store Configuration

This document describes the updated AWS Parameter Store implementation for environment-based configuration loading.

## Overview

The application now supports automatic configuration loading based on the environment:

- **Production Environment (`NODE_ENV=production`)**: Loads configuration from AWS Parameter Store
- **Non-Production Environments**: Loads configuration from local `.env` files

## Implementation Details

### Environment-Based Configuration Loading

#### Production Environment
- **Trigger**: `NODE_ENV=production`
- **Source**: AWS Parameter Store
- **Path Prefix**: `/reach/prod/api/`
- **Region**: `eu-west-1` (configurable via `AWS_REGION`)
- **Authentication**: IAM roles (no hardcoded credentials)
- **Fallback**: Local `.env` file if Parameter Store is unavailable

#### Non-Production Environments
- **Trigger**: `NODE_ENV` != `production`
- **Source**: Local `.env` file only
- **Behavior**: Standard dotenv loading

### Parameter Store Configuration

#### AWS Configuration
- **Region**: `eu-west-1`
- **Parameter Path Prefix**: `/reach/prod/api/`
- **Authentication**: IAM roles (automatic when running on AWS infrastructure)

#### Parameter Types Supported
1. **String**: Standard string values (most parameters)
2. **SecureString**: Encrypted sensitive values (secrets, tokens, passwords)
3. **StringList**: Comma-separated list values (e.g., `API_DOC_PASSWORD`)

#### Parameter Mapping
Parameters are automatically mapped from Parameter Store paths to environment variables:
- Parameter: `/reach/prod/api/database/url` → Environment Variable: `DATABASE_URL`
- Parameter: `/reach/prod/api/redis/host` → Environment Variable: `REDIS_HOST`
- Parameter: `/reach/prod/api/smtp/password` → Environment Variable: `SMTP_PASSWORD`

### Caching and Performance

#### Caching Strategy
- **Cache Duration**: 5 minutes (300 seconds)
- **Cache Scope**: In-memory, per application instance
- **Cache Invalidation**: Automatic expiry, manual clear available

#### Performance Optimizations
- **Pagination**: Automatic handling of large parameter sets (57+ parameters)
- **Batch Loading**: Loads all parameters in paginated batches
- **Lazy Loading**: Only loads in production environment

### Error Handling and Fallback

#### Graceful Fallback
1. **Parameter Store Unavailable**: Falls back to `.env` file
2. **Authentication Issues**: Falls back to `.env` file
3. **Network Issues**: Falls back to `.env` file
4. **Invalid Parameters**: Logs warnings, continues with valid parameters

#### Error Logging
- Comprehensive error details logged
- Sensitive information masked in logs
- Sample configuration values logged (non-sensitive only)

## Configuration Requirements

### Environment Variables (Required)
All environment variables must be present either in Parameter Store (production) or `.env` file (non-production):

```bash
# Core Application
NODE_ENV=production
PORT=8000
APP_NAME="Touching Student Lives"

# Database
DATABASE_URL=**********************************/database

# AWS Configuration
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=...
AWS_BUCKET_NAME=bucket-name
AWS_ENDPOINT=https://s3.eu-west-1.amazonaws.com

# Email Configuration
SMTP_USERNAME=username
SMTP_PASSWORD=password
SMTP_ENDPOINT=smtp.example.com
SMTP_PORT=587
SENDER_EMAIL=<EMAIL>
EMAIL_TOGGLE=ON

# Authentication
ACCESS_TOKEN_SECRET=secret
ACCESS_TOKEN_EXPIRY=15m
REFRESH_TOKEN_SECRET=secret
REFRESH_TOKEN_EXPIRY=7d
MAGIC_LINK_EXPIRY=10m
OTP_HASH_SECRET=secret
OTP_EXPIRY_TIME=300

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=password
REDIS_USERNAME=username
REDIS_DB=0
REDIS_TLS=false
REDIS_MODE=single

# Queue Configuration
QUEUE_CONCURRENCY=5
QUEUE_ATTEMPTS=3
QUEUE_BACKOFF_DELAY=1000
QUEUE_REMOVE_ON_COMPLETE=true
QUEUE_REMOVE_ON_FAIL=false

# Bull Board UI
BULL_BOARD_USERNAME=admin
BULL_BOARD_PASSWORD=secure-password

# Cache Configuration
CACHE_ENABLED=true
CACHE_VERSION=1
CACHE_NAMESPACE=app
CACHE_DEFAULT_TTL=3600
CACHE_WARMUP_ENABLED=true

# Additional Configuration
THROTTLE_TTL=60
THROTTLE_LIMIT=10
FIREBASE_SERVICE_ACCOUNT_BASE64=base64-encoded-json
SENTRY_DSN=https://...
MAX_FILE_SIZE=********
```

### Parameter Store Structure
Parameters should be organized under `/reach/prod/api/` with the following structure:

```
/reach/prod/api/
├── database/
│   └── url (SecureString)
├── smtp/
│   ├── username (String)
│   ├── password (SecureString)
│   ├── endpoint (String)
│   └── port (String)
├── aws/
│   ├── access_key_id (SecureString)
│   ├── secret_access_key (SecureString)
│   ├── bucket_name (String)
│   └── endpoint (String)
├── redis/
│   ├── host (String)
│   ├── port (String)
│   ├── password (SecureString)
│   └── username (String)
└── ... (other parameters)
```

## Usage

### Application Startup
The configuration loading is automatic and happens during application bootstrap:

```typescript
// Automatically called in main.ts
await bootstrapConfig();
```

### Manual Cache Management
```typescript
import { ParameterStoreConfigProvider } from './aws/parameter-store-config.provider';

// Clear cache manually
ParameterStoreConfigProvider.clearCache();

// Check cache validity
const isValid = ParameterStoreConfigProvider.isCacheValid();
```

## Deployment Considerations

### IAM Permissions
The application requires the following IAM permissions for Parameter Store access:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ssm:GetParametersByPath",
        "ssm:GetParameter",
        "ssm:GetParameters"
      ],
      "Resource": "arn:aws:ssm:eu-west-1:*:parameter/reach/prod/api/*"
    }
  ]
}
```

### Security Best Practices
1. **Use IAM Roles**: No hardcoded AWS credentials in code
2. **SecureString Parameters**: Use for sensitive data (passwords, tokens, secrets)
3. **Least Privilege**: Grant minimal required permissions
4. **Parameter Encryption**: Enable encryption for sensitive parameters
5. **Access Logging**: Monitor Parameter Store access via CloudTrail

### Monitoring and Troubleshooting
1. **Application Logs**: Check for Parameter Store loading messages
2. **CloudWatch**: Monitor Parameter Store API calls
3. **Fallback Behavior**: Verify `.env` fallback works correctly
4. **Cache Performance**: Monitor cache hit rates and expiry

## Testing

### Local Testing
```bash
# Test non-production environment
NODE_ENV=development npm start

# Test production environment (with fallback)
NODE_ENV=production npm start
```

### Validation Script
```bash
# Run the Parameter Store implementation test
node test-parameter-store-implementation.js
```

## Migration Guide

### From Previous Implementation
1. **Remove Environment Variables**: No longer need `AWS_PARAMETER_STORE_ENABLED`
2. **Update IAM Roles**: Ensure proper permissions for `/reach/prod/api/` path
3. **Verify Parameters**: Ensure all 57+ parameters are in Parameter Store
4. **Test Fallback**: Verify `.env` fallback works in case of Parameter Store issues

### Parameter Store Setup
1. Create parameters under `/reach/prod/api/` prefix
2. Use appropriate parameter types (String, SecureString, StringList)
3. Set proper IAM permissions
4. Test parameter retrieval

## Troubleshooting

### Common Issues
1. **Authentication Errors**: Check IAM role permissions
2. **Parameter Not Found**: Verify parameter path and prefix
3. **Validation Errors**: Check parameter values and types
4. **Network Issues**: Verify AWS region and connectivity

### Debug Mode
Enable debug logging to see detailed parameter loading information:
```bash
LOG_LEVEL=debug NODE_ENV=production npm start
```
