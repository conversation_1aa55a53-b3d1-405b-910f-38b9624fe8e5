# Firebase Configuration Fix Guide

## Issue Summary
The `FIREBASE_SERVICE_ACCOUNT_BASE64` parameter in AWS Parameter Store contains `[SECURE VALUE]` instead of a valid base64-encoded Firebase service account JSON, causing the application to fail with:
```
SyntaxError: Unexpected token 'e', "test" is not valid JSON
```

## Solution Steps

### Step 1: Obtain Firebase Service Account JSON

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project** (or create one if needed)
3. **Navigate to Project Settings**:
   - Click the gear icon ⚙️ in the left sidebar
   - Select "Project settings"
4. **Go to Service Accounts tab**
5. **Generate new private key**:
   - Click "Generate new private key"
   - Download the JSON file (e.g., `firebase-service-account.json`)

### Step 2: Convert to Base64

Convert the Firebase service account JSON to base64 encoding:

```bash
# Method 1: Using cat and base64 (Linux/macOS)
cat firebase-service-account.json | base64 -w 0

# Method 2: Using base64 directly (macOS)
base64 -i firebase-service-account.json

# Method 3: Using Node.js
node -e "console.log(Buffer.from(require('fs').readFileSync('firebase-service-account.json')).toString('base64'))"
```

**Important**: The output should be a single long string without line breaks.

### Step 3: Update Parameter Store

Update the Parameter Store parameter with the base64-encoded value:

```bash
# Using AWS CLI
aws ssm put-parameter \
  --name "/reach/prod/api/FIREBASE_SERVICE_ACCOUNT_BASE64" \
  --value "YOUR_BASE64_ENCODED_JSON_HERE" \
  --type "SecureString" \
  --overwrite \
  --region eu-west-1
```

**Or using AWS Console**:
1. Go to AWS Systems Manager → Parameter Store
2. Find `/reach/prod/api/FIREBASE_SERVICE_ACCOUNT_BASE64`
3. Click "Edit"
4. Replace the value with your base64-encoded JSON
5. Save changes

### Step 4: Verify the Configuration

Run the validation script to verify the configuration:

```bash
# Test the Firebase configuration
node scripts/validate-firebase-config.js

# Test application startup
npm run start:dev
```

## Expected Firebase Service Account JSON Structure

Your Firebase service account JSON should look like this:

```json
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## Validation Checklist

✅ **Required Fields Present**:
- `type` = "service_account"
- `project_id` (your Firebase project ID)
- `private_key` (begins with -----BEGIN PRIVATE KEY-----)
- `client_email` (Firebase service account email)
- `client_id`
- `auth_uri`
- `token_uri`

✅ **Base64 Encoding**:
- Single line output (no line breaks)
- Valid base64 characters only
- Can be decoded back to valid JSON

✅ **Parameter Store**:
- Parameter name: `/reach/prod/api/FIREBASE_SERVICE_ACCOUNT_BASE64`
- Type: SecureString
- Region: eu-west-1

## Testing the Fix

After updating the Parameter Store:

1. **Test in Development** (uses .env fallback):
   ```bash
   NODE_ENV=development npm run start:dev
   ```

2. **Test in Production** (uses Parameter Store):
   ```bash
   NODE_ENV=production npm run start:prod
   ```

3. **Verify Firebase functionality**:
   - Check application logs for "Firebase Admin SDK initialized successfully"
   - Test push notification functionality
   - Verify no Firebase-related errors in startup

## Troubleshooting

### Common Issues:

1. **"Invalid placeholder value"**:
   - The Parameter Store still contains "test" or similar placeholder
   - Solution: Update with valid base64-encoded Firebase JSON

2. **"Failed to decode Firebase service account base64"**:
   - Invalid base64 encoding
   - Solution: Re-encode the JSON file properly

3. **"Failed to parse Firebase service account JSON"**:
   - Base64 decodes to invalid JSON
   - Solution: Verify the original JSON file is valid

4. **"Missing required fields"**:
   - Incomplete Firebase service account JSON
   - Solution: Download a fresh service account JSON from Firebase Console

5. **"Invalid Firebase service account type"**:
   - Wrong type of JSON file (not a service account)
   - Solution: Ensure you downloaded the service account JSON, not another type

## Security Notes

- ✅ Firebase service account JSON contains sensitive private keys
- ✅ Always use SecureString type in Parameter Store
- ✅ Never commit Firebase service account JSON to version control
- ✅ Rotate service account keys periodically
- ✅ Use IAM roles for Parameter Store access in production

## Quick Fix Command

If you have the Firebase service account JSON file ready:

```bash
# One-liner to update Parameter Store
aws ssm put-parameter \
  --name "/reach/prod/api/FIREBASE_SERVICE_ACCOUNT_BASE64" \
  --value "$(cat firebase-service-account.json | base64 -w 0)" \
  --type "SecureString" \
  --overwrite \
  --region eu-west-1
```
